name: Test

on:
  push:
    branches:
      - main

  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: ${{ matrix.os }}

    timeout-minutes: 10

    strategy:
      matrix:
        node_version: [lts/*]
        os: [ubuntu-latest, windows-latest]
      fail-fast: false

    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4

      - name: Set node version to ${{ matrix.node_version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node_version }}
          cache: pnpm

      - name: Install
        run: pnpm i

      - name: Build
        run: pnpm run build

      - name: Test
        run: pnpm run test

      - name: Lint
        run: pnpm run lint

      - name: TypeCheck
        run: pnpm run typecheck
