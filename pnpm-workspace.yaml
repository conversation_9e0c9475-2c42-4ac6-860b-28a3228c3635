packages: []
catalog:
  element-plus: ^2.10.4
catalogs:
  build:
    '@iconify-json/carbon': ^1.2.9
    '@vitejs/plugin-vue': ^5.2.4
    jsdom: ^26.1.0
    unocss: ^66.2.0
    unplugin: ^2.3.5
    unplugin-auto-import: ^19.3.0
    unplugin-vue-components: ^28.7.0
    unplugin-vue-macros: ^2.14.5
    unplugin-vue-router: ^0.12.0
    vite: ^6.3.5
  'conflicts_element-plus_:':
    element-plus: ':'
  dev:
    '@antfu/eslint-config': ^4.14.1
    '@types/node': ^22.15.31
    '@unocss/eslint-plugin': ^66.2.0
    '@vue-macros/volar': ^3.0.0-beta.14
    '@vue/test-utils': ^2.4.6
    eslint: ^9.28.0
    eslint-plugin-format: ^1.0.1
    lint-staged: ^16.1.0
    simple-git-hooks: ^2.13.0
    taze: ^19.1.0
    typescript: ^5.8.3
    vitest: ^3.2.3
    vue-tsc: ^2.2.10
  frontend:
    '@vueuse/core': ^13.3.0
    vue: ^3.5.16
    vue-router: ^4.5.1
onlyBuiltDependencies:
  - esbuild
  - simple-git-hooks
  - unrs-resolver
