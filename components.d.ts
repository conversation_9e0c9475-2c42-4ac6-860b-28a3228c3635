/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TheCounter: typeof import('./src/components/TheCounter.vue')['default']
    TheFooter: typeof import('./src/components/TheFooter.vue')['default']
    TheInput: typeof import('./src/components/TheInput.vue')['default']
  }
}
